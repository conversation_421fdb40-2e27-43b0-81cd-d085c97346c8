import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'

export async function POST() {
  try {
    console.log('🧹 Cleanup: Starting abandoned orders cleanup...')

    const now = new Date()
    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    const seventyTwoHoursAgo = new Date(now.getTime() - 72 * 60 * 60 * 1000)

    // Find abandoned orders using progressive cleanup logic:
    // 1. Orders > 24h old WITHOUT recovery email, OR
    // 2. Orders > 72h old WITH recovery email (48h grace period after email)
    const { data: abandonedOrders, error: fetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        id,
        payment_intent_id,
        status,
        payment_status,
        created_at,
        email,
        cart_recovery_sent_at
      `)
      .eq('status', 'pending')
      .eq('payment_status', 'pending')
      .or(`and(created_at.lt.${twentyFourHoursAgo.toISOString()},cart_recovery_sent_at.is.null),and(created_at.lt.${seventyTwoHoursAgo.toISOString()},cart_recovery_sent_at.not.is.null)`)

    if (fetchError) {
      console.error('🧹 Cleanup: Error fetching abandoned orders:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch abandoned orders' },
        { status: 500 }
      )
    }

    if (!abandonedOrders || abandonedOrders.length === 0) {
      console.log('🧹 Cleanup: No abandoned orders found')
      return NextResponse.json({
        success: true,
        message: 'No abandoned orders found',
        cleanedCount: 0
      })
    }

    console.log(`🧹 Cleanup: Found ${abandonedOrders.length} abandoned orders`)

    // Cancel abandoned orders
    const { data: cancelledOrders, error: cancelError } = await supabaseAdmin
      .from('orders')
      .update({
        status: 'cancelled',
        payment_status: 'failed',
        notes: 'Manually cancelled - abandoned checkout (24h without recovery email or 72h with recovery email)'
      })
      .in('id', abandonedOrders.map(order => order.id))
      .select()

    if (cancelError) {
      console.error('🧹 Cleanup: Error cancelling abandoned orders:', cancelError)
      return NextResponse.json(
        { error: 'Failed to cancel abandoned orders' },
        { status: 500 }
      )
    }

    // Optional: Cancel Stripe Payment Intents for abandoned orders
    const { default: Stripe } = await import('stripe')
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!)
    let stripeCancelledCount = 0

    for (const order of abandonedOrders) {
      if (order.payment_intent_id) {
        try {
          await stripe.paymentIntents.cancel(order.payment_intent_id)
          stripeCancelledCount++
          console.log(`🧹 Cleanup: Cancelled Stripe Payment Intent ${order.payment_intent_id}`)
        } catch (stripeError) {
          console.error(`🧹 Cleanup: Failed to cancel Stripe Payment Intent ${order.payment_intent_id}:`, stripeError)
          // Continue with other orders even if one fails
        }
      }
    }

    console.log(`🧹 Cleanup: Successfully cancelled ${cancelledOrders?.length || 0} orders and ${stripeCancelledCount} Stripe Payment Intents`)

    return NextResponse.json({
      success: true,
      message: `Cleaned up ${cancelledOrders?.length || 0} abandoned orders`,
      cleanedCount: cancelledOrders?.length || 0,
      stripeCancelledCount,
      abandonedOrders: abandonedOrders.map(order => ({
        id: order.id,
        email: order.email,
        created_at: order.created_at
      }))
    })

  } catch (error) {
    console.error('🧹 Cleanup: Error in abandoned orders cleanup:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint to check for abandoned orders without cleaning them
export async function GET() {
  try {
    console.log('🧹 Cleanup: Checking for abandoned orders...')

    // Get ALL pending orders (not just > 24h) for admin visibility
    // This allows admin to see recent abandoned carts too
    const { data: abandonedOrders, error: fetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        id,
        payment_intent_id,
        status,
        payment_status,
        created_at,
        email,
        total_amount,
        cart_recovery_sent_at
      `)
      .eq('status', 'pending')
      .eq('payment_status', 'pending')
      .order('created_at', { ascending: false })

    if (fetchError) {
      console.error('🧹 Cleanup: Error fetching abandoned orders:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch abandoned orders' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      count: abandonedOrders?.length || 0,
      abandonedOrders: abandonedOrders?.map(order => ({
        id: order.id,
        email: order.email,
        total_amount: order.total_amount,
        created_at: order.created_at,
        hours_since_creation: Math.floor(
          (new Date().getTime() - new Date(order.created_at).getTime()) / (1000 * 60 * 60)
        )
      })) || []
    })

  } catch (error) {
    console.error('🧹 Cleanup: Error checking abandoned orders:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
